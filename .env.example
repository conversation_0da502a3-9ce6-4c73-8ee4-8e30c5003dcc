# WhatsApp Spotify Bot Configuration
# Copy this file to .env and fill in your actual values

# Spotify API Credentials
# Get these from https://developer.spotify.com/dashboard/
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here

# WhatsApp Configuration
WHATSAPP_PROFILE_PATH=./whatsapp_profile

# Logging Configuration
LOG_LEVEL=INFO

# Optional: Redis Configuration (for queue management)
REDIS_URL=redis://localhost:6379/0

# Optional: Database Configuration
DATABASE_URL=sqlite:///whatsapp_bot.db

# Security
BOT_TOKEN=your_secure_bot_token_here
ALLOWED_USERS=+1234567890,+0987654321  # Comma-separated phone numbers

# Performance Settings
MAX_CONCURRENT_DOWNLOADS=3
DOWNLOAD_TIMEOUT=300
MAX_FILE_SIZE_MB=50
