"""
Configuration settings for WhatsApp Spotify Bot
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base directories
BASE_DIR = Path(__file__).parent.parent
DOWNLOADS_DIR = BASE_DIR / "downloads"
LOGS_DIR = BASE_DIR / "logs"

# Ensure directories exist
DOWNLOADS_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# WhatsApp Configuration
WHATSAPP_CONFIG = {
    "profile_path": os.getenv("WHATSAPP_PROFILE_PATH", "./whatsapp_profile"),
    "headless": True,
    "timeout": 60,
    "retry_attempts": 3,
    "retry_delay": 5,
}

# Spotify Configuration
SPOTIFY_CONFIG = {
    "client_id": os.getenv("SPOTIFY_CLIENT_ID"),
    "client_secret": os.getenv("SPOTIFY_CLIENT_SECRET"),
    "download_format": "mp3",
    "audio_quality": "128k",
    "max_file_size_mb": 50,
    "max_playlist_size": 50,
}

# Download Configuration
DOWNLOAD_CONFIG = {
    "output_directory": str(DOWNLOADS_DIR),
    "temp_directory": str(DOWNLOADS_DIR / "temp"),
    "cleanup_after_send": True,
    "max_concurrent_downloads": 3,
    "download_timeout": 300,  # 5 minutes
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": os.getenv("LOG_LEVEL", "INFO"),
    "file_path": str(LOGS_DIR / "whatsapp_bot.log"),
    "max_file_size": "10 MB",
    "backup_count": 5,
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
}

# Rate Limiting
RATE_LIMIT_CONFIG = {
    "max_requests_per_user_per_hour": 10,
    "max_requests_per_user_per_day": 50,
    "cooldown_period_seconds": 60,
}

# Error Handling
ERROR_CONFIG = {
    "max_retries": 3,
    "retry_delay": 2,
    "send_error_messages": True,
    "detailed_error_logs": True,
}

# Message Templates
MESSAGE_TEMPLATES = {
    "welcome": "🎵 Welcome to Spotify Bot! Send me a Spotify URL or song name to download.",
    "processing": "🔄 Processing your request... Please wait.",
    "download_complete": "✅ Download complete! Sending your file(s)...",
    "error_invalid_url": "❌ Invalid Spotify URL. Please send a valid Spotify track or playlist URL.",
    "error_not_found": "❌ Song not found. Please check the URL or try a different search term.",
    "error_download_failed": "❌ Download failed. Please try again later.",
    "error_file_too_large": "❌ File is too large to send. Maximum size is {max_size}MB.",
    "error_rate_limit": "⏰ You've reached the rate limit. Please wait before sending another request.",
    "playlist_info": "📋 Found playlist with {count} tracks. Starting download...",
    "playlist_progress": "📥 Downloaded {current}/{total}: {title}",
}

# Validation Rules
VALIDATION_CONFIG = {
    "spotify_url_patterns": [
        r"https://open\.spotify\.com/track/([a-zA-Z0-9]+)",
        r"https://open\.spotify\.com/playlist/([a-zA-Z0-9]+)",
        r"https://open\.spotify\.com/album/([a-zA-Z0-9]+)",
    ],
    "max_message_length": 1000,
    "allowed_file_extensions": [".mp3", ".m4a"],
    "min_song_duration": 10,  # seconds
    "max_song_duration": 600,  # 10 minutes
}
